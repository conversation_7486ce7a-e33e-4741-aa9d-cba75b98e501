"use client"

import { useState, useEffect, useRef, useC<PERSON>back, useMemo } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { MessageSquare, FileText, Mic, Loader, Info, Book, X, MessageCircle } from "lucide-react"
import { CompactThemeToggle } from "../ThemeToggle"
import ChatTab from "./ChatTab"
import { getFirestore, collection, query, where, getDocs, addDoc, serverTimestamp, orderBy, limit, doc, getDoc } from "firebase/firestore"
import { useGetNamespace } from "./useGetNamespace"
import { useSession } from "next-auth/react"
import { v4 as uuidv4 } from "uuid"
import useUpload, { StatusText } from "./useUpload"
import { useConversation } from "@elevenlabs/react"
import { db } from "components/firebase"
import SideBar from "./SideBar"
import Rehearsals from "./Rehearsals"
import FileDetails from "./FileDetails"
import ScriptTab from "./ScriptTab"
import ResponseTab from "./ResponseTab"
import { getAgentConfiguration, extractAgentName, createElevenLabsClient, configureAgentClientTools, checkScriptInAgentKnowledgeBase, synchronizeScriptToAgent } from "./elevenlabs"
import { useAgentModality } from "./useAgentModality"
import { AVAILABLE_VOICES } from "./voiceUtils"
import { useUserAgent } from "../../hooks/useUserAgent"
import { useAgentConfigValidation } from "../../hooks/useAgentConfigValidation"

interface ScriptFile {
id: string
name: string
namespace: string
}

interface ScriptValidationStatus {
isValidating: boolean
isSynchronizing: boolean
firebaseExists: boolean
elevenLabsExists: boolean
isReady: boolean
statusMessage: string
error?: string
}

interface ChatMessage {
id?: string
tempId?: string
role: "user" | "assistant"
content: string
timestamp: string
audioUrl?: string
fileDocumentId?: string
}

interface ReadermodalProps {
isOpen: boolean
onClose: () => void
fileId?: string
}

function Readermodal({ isOpen, onClose, fileId }: ReadermodalProps) {
const [activeTab, setActiveTab] = useState<string | null>(null)
const [activeSection, setActiveSection] = useState<string>("rehearsing")
const [scriptFiles, setScriptFiles] = useState<ScriptFile[]>([])
const [loading, setLoading] = useState(true)
const [error, setError] = useState<string | null>(null)
const [isUploading, setIsUploading] = useState<boolean>(false)
const [uploadProgress, setUploadProgress] = useState<number | null>(null)
const [uploadStatusText, setUploadStatusText] = useState<string>("Uploading script...")
const fileInputRef = useRef<HTMLInputElement>(null)
const [chatId, setChatId] = useState<string | null>(null)
const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
const [fileDocumentId, setFileDocumentId] = useState<string | null>(null)
const [selectedFileNamespace, setSelectedFileNamespace] = useState<string | null>(null)
const [fileName, setFileName] = useState<string | null>(null)
const isMounted = useRef(true)
const messagesProcessingRef = useRef(false)
const [hasPermission, setHasPermission] = useState(false)
const [isMuted, setIsMuted] = useState(false)
const [isListening, setIsListening] = useState(false)
const [voiceErrorMessage, setVoiceErrorMessage] = useState("")
const [apiConfigStatus, setApiConfigStatus] = useState<'unchecked' | 'valid' | 'invalid' | 'connecting'>('unchecked')
const [detailedErrorInfo, setDetailedErrorInfo] = useState<string | null>(null)
const { data: session, status: sessionStatus } = useSession()
const userId = session?.user?.email || ""
const { handleUpload, progress, status, error: uploadError } = useUpload()
const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)
const [scriptContent, setScriptContent] = useState<string>("")
const [isScriptLoading, setIsScriptLoading] = useState<boolean>(false)
const [isScriptReady, setIsScriptReady] = useState<boolean>(false)
const [isFormatting, setIsFormatting] = useState<boolean>(false)
const [formattedMarkdown, setFormattedMarkdown] = useState<string>("")

// Script validation and synchronization state
const [scriptValidationStatus, setScriptValidationStatus] = useState<ScriptValidationStatus>({
isValidating: false,
isSynchronizing: false,
firebaseExists: false,
elevenLabsExists: false,
isReady: false,
statusMessage: ""
})
const [selectedVoiceId, setSelectedVoiceId] = useState<string | null>(null)
const [isUpdatingVoice, setIsUpdatingVoice] = useState(false)
const { agentModality, setAgentModality, updateAgentPrompt } = useAgentModality()
const [agentName, setAgentName] = useState<string | null>(null)
const [conversationMessages, setConversationMessages] = useState<Array<{
id: string;
type: "user" | "assistant";
content: string;
timestamp: Date;
}>>([])
const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null)
const [sessionDuration, setSessionDuration] = useState(0)
const [isRecording, setIsRecording] = useState(false)
const [recordingError, setRecordingError] = useState<string | null>(null)
const [recordingMode, setRecordingMode] = useState<'audio' | 'video'>('audio')
const [recordingDuration, setRecordingDuration] = useState(0)
const mediaRecorderRef = useRef<MediaRecorder | null>(null)
const [currentStream, setCurrentStream] = useState<MediaStream | null>(null)
const [hasCameraPermission, setHasCameraPermission] = useState(false)
const recordingTimerRef = useRef<NodeJS.Timeout | null>(null)
const [recordings, setRecordings] = useState<Array<{
id: string;
filename: string;
url: string;
timestamp: Date;
rehearsalId: string;
type: 'audio' | 'video';
duration?: number;
fileSize?: number;
}>>([])
const [playingRecording, setPlayingRecording] = useState<string | null>(null)
const [audioElements, setAudioElements] = useState<Record<string, HTMLAudioElement>>({})
const [agentConfigState, setAgentConfigState] = useState<'idle' | 'updating' | 'ready' | 'error'>('idle')
const [agentConfigMessage, setAgentConfigMessage] = useState<string>('Select a voice and script to begin.')

// User-specific agent management
const { agentId: userAgentId, isLoading: isUserAgentLoading, error: userAgentError, refetch: refetchUserAgent } = useUserAgent()

// Agent configuration validation for text response transmission
const {
  isValidating: isValidatingAgentConfig,
  validationResult: agentConfigValidationResult,
  validateConfiguration: validateAgentConfiguration,
  clearResult: clearValidationResult
} = useAgentConfigValidation()

const clientTools = useMemo(() => {
const tools: Record<string, (parameters: any) => any> = {};
tools['switch_to_script_tab'] = (parameters: any) => {
console.log("[CLIENT_TOOL] 🔧 switch_to_script_tab called with parameters:", parameters);
const isReady = parameters?.ready === true || parameters?.user_ready === true;
if (isReady) {
console.log("[CLIENT_TOOL] ✅ User confirmed ready - tool acknowledged (no automatic UI changes)");
return {
success: true,
message: "User readiness confirmed. Please manually navigate to the Script tab to view your script."
};
} else {
console.warn("[CLIENT_TOOL] ⚠️ Tool called but 'ready' parameter was not true:", parameters);
return {
success: false,
message: "User readiness was not confirmed in the tool parameters."
};
}
};
return tools;
}, []);

// Debug function to log all conversation events
const logConversationEvent = (eventName: string, data: any) => {
console.log(`[CONVERSATION_DEBUG] Event: ${eventName}`, data)
}

// Comprehensive debugging function to diagnose message capture issues
const debugMessageCaptureSetup = () => {
console.log("[MESSAGE_CAPTURE_DEBUG] 🔍 Comprehensive Message Capture Diagnosis")
console.log("[MESSAGE_CAPTURE_DEBUG] ==========================================")

console.log("[MESSAGE_CAPTURE_DEBUG] 1. Conversation Object Analysis:")
console.log("[MESSAGE_CAPTURE_DEBUG]   - Conversation object exists:", !!conversation)
console.log("[MESSAGE_CAPTURE_DEBUG]   - Conversation status:", conversation?.status)
console.log("[MESSAGE_CAPTURE_DEBUG]   - Conversation methods:", conversation ? Object.keys(conversation) : "N/A")

console.log("[MESSAGE_CAPTURE_DEBUG] 2. Agent Configuration:")
console.log("[MESSAGE_CAPTURE_DEBUG]   - User Agent ID:", userAgentId)
console.log("[MESSAGE_CAPTURE_DEBUG]   - Is User Agent Loading:", isUserAgentLoading)
console.log("[MESSAGE_CAPTURE_DEBUG]   - User Agent Error:", userAgentError)
console.log("[MESSAGE_CAPTURE_DEBUG]   - Agent Config State:", agentConfigState)

console.log("[MESSAGE_CAPTURE_DEBUG] 3. Connection State:")
console.log("[MESSAGE_CAPTURE_DEBUG]   - API Config Status:", apiConfigStatus)
console.log("[MESSAGE_CAPTURE_DEBUG]   - Is Listening:", isListening)
console.log("[MESSAGE_CAPTURE_DEBUG]   - Voice Status:", voiceStatus)
console.log("[MESSAGE_CAPTURE_DEBUG]   - Is Speaking:", isSpeaking)

console.log("[MESSAGE_CAPTURE_DEBUG] 4. Message State:")
console.log("[MESSAGE_CAPTURE_DEBUG]   - Current Messages Count:", conversationMessages.length)
console.log("[MESSAGE_CAPTURE_DEBUG]   - Last Message:", conversationMessages[conversationMessages.length - 1])

console.log("[MESSAGE_CAPTURE_DEBUG] 5. Session State:")
console.log("[MESSAGE_CAPTURE_DEBUG]   - Session Start Time:", sessionStartTime)
console.log("[MESSAGE_CAPTURE_DEBUG]   - Session Duration:", sessionDuration)
console.log("[MESSAGE_CAPTURE_DEBUG]   - Active Tab:", activeTab)
console.log("[MESSAGE_CAPTURE_DEBUG]   - File Name:", fileName)

console.log("[MESSAGE_CAPTURE_DEBUG] ==========================================")
console.log("[MESSAGE_CAPTURE_DEBUG] 💡 If onMessage is not being called during live conversations,")
console.log("[MESSAGE_CAPTURE_DEBUG]    the issue is likely in the ElevenLabs SDK or agent configuration.")
console.log("[MESSAGE_CAPTURE_DEBUG]    Check the Network tab for WebSocket activity during conversations.")


}

// Test function to add sample messages for debugging
const addTestMessages = () => {
const testMessages = [
{
id: uuidv4(),
type: "user" as const,
content: "Hello, I'm ready to rehearse my lines.",
timestamp: new Date()
},
{
id: uuidv4(),
type: "assistant" as const,
content: "Great! I'm Morgan, your line running partner for memorization. I have your script ready for line running. Ready to run lines when you are.",
timestamp: new Date()
}
]
setConversationMessages(testMessages)
console.log("[CONVERSATION_DEBUG] Added test messages for debugging")
}

// The shared agent ID is now a fallback. The user-specific agent is primary.
const sharedAgentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
const effectiveAgentId = userAgentId || sharedAgentId

// =================================================================================
// ⭐️ FIXED: Step 1 of 2 - `useConversation` now uses the effectiveAgentId
// This ensures the conversation hook connects to the user-specific agent once available.
// =================================================================================
const conversation = useConversation({
apiKey: process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************',
agentId: effectiveAgentId, // CRITICAL: Use the user-specific agent when available, fallback to shared.
maxReconnectAttempts: 3,
reconnectInterval: 2000,
clientTools: clientTools,
onConnect: () => {
logConversationEvent("onConnect", "Connected to ElevenLabs API")
console.log("[DEBUG_CONNECTION] 🔗 Connected to ElevenLabs API successfully")
console.log("[DEBUG_CONNECTION] Connection details:", {
apiKey: (process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************').substring(0, 10) + '...',
conversationAgentId: effectiveAgentId, // Now reflects the active agent
userAgentId: userAgentId,
sharedAgentId: sharedAgentId,
userEmail: session?.user?.email,
conversationObject: !!conversation,
conversationMethods: conversation ? Object.keys(conversation) : []
})
console.log("[DEBUG_CONNECTION] 🎯 onMessage handler should now be active and ready to receive messages")
console.log("[DEBUG_CONNECTION] 🔍 Testing if conversation object has message handling capabilities...")

// Test if the conversation object is properly set up
  if (conversation) {
    console.log("[DEBUG_CONNECTION] Conversation object properties:", Object.keys(conversation))
    console.log("[DEBUG_CONNECTION] Conversation status:", conversation.status)
  }

  setApiConfigStatus('valid')
  setDetailedErrorInfo(null)
  setSessionStartTime(new Date())
  setConversationMessages([])
},
onDisconnect: (reason?: any) => {
  logConversationEvent("onDisconnect", reason)
  console.log("[DEBUG_CONNECTION] 🔌 Disconnected from ElevenLabs")
  console.log("[DEBUG_CONNECTION] Disconnect reason:", reason)
  console.log("[DEBUG_CONNECTION] Connection was active for:", sessionStartTime ? (Date.now() - sessionStartTime.getTime()) / 1000 : 0, "seconds")
  console.log("[DEBUG_CONNECTION] Final conversation messages count:", conversationMessages.length)
  setIsListening(false)
  setSessionStartTime(null)
  setSessionDuration(0)
  // Don't clear conversation messages on disconnect - preserve the log
  if (apiConfigStatus === 'valid' && reason !== 'user_initiated') {
    setVoiceErrorMessage("Connection lost. Click 'Start Rehearsing' to reconnect.")
    setApiConfigStatus('unchecked')
  } else {
    setVoiceErrorMessage("")
    setApiConfigStatus('unchecked')
  }
},
onError: (error: unknown) => {
  console.error("ElevenLabs API error:", error)
  console.error("Error details:", {
    type: typeof error,
    constructor: error?.constructor?.name,
    message: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined
  })
  if (error && typeof error === 'object' && 'type' in error && error.type === 'close') {
    const closeCode = (error as any).code
    const closeReason = (error as any).reason
    const wasClean = (error as any).wasClean
    console.error("WebSocket CloseEvent detected:", {
      code: closeCode,
      reason: closeReason,
      wasClean: wasClean
    })
    let errorMessage = "Connection closed unexpectedly."
    if (closeCode === 1006) {
      errorMessage = "Connection lost abnormally. This may be a network issue."
    } else if (closeCode === 1011) {
      errorMessage = "Server error occurred. Please try again."
    } else if (closeCode === 4001) {
      errorMessage = "Authentication failed. Please check your API key."
    } else if (closeCode === 4003) {
      errorMessage = "Agent not found. Please check your agent ID."
    } else if (closeReason) {
      errorMessage = `Connection closed: ${closeReason}`
    }
    setVoiceErrorMessage(errorMessage)
    setDetailedErrorInfo(`WebSocket closed with code ${closeCode}: ${closeReason || 'No reason provided'}`)
  } else {
    const errorMessage = error instanceof Error ? error.message : String(error)
    setDetailedErrorInfo(errorMessage)
    setVoiceErrorMessage("Connection error: " + errorMessage.substring(0, 100))
  }
  setApiConfigStatus('invalid')
  if (isListening) {
    setIsListening(false)
  }
},
onMessage: (message) => {
  console.log("[DEBUG_MESSAGE_FLOW] 🔍 onMessage handler triggered!")
  console.log("[DEBUG_MESSAGE_FLOW] Message type:", typeof message)
  console.log("[DEBUG_MESSAGE_FLOW] Message content:", message)
  console.log("[DEBUG_MESSAGE_FLOW] Message keys:", message && typeof message === "object" ? Object.keys(message) : "N/A")

  if (typeof message === "string") {
    console.log("[AUDIO] Voice input received:", message)
    // Add user message to conversation log
    const userMessage = {
      id: uuidv4(),
      type: "user" as const,
      content: message,
      timestamp: new Date()
    }
    console.log("[DEBUG_MESSAGE_FLOW] ✅ Adding user message to state:", userMessage)
    setConversationMessages(prev => {
      console.log("[DEBUG_MESSAGE_FLOW] Previous messages count:", prev.length)
      const newMessages = [...prev, userMessage]
      console.log("[DEBUG_MESSAGE_FLOW] New messages count:", newMessages.length)
      return newMessages
    })
  } else if (message && typeof message === "object" && 'message' in message) {
    console.log("[AUDIO] AI response received:", message.message)
    // Add assistant message to conversation log
    const assistantMessage = {
      id: uuidv4(),
      type: "assistant" as const,
      content: message.message,
      timestamp: new Date()
    }
    console.log("[DEBUG_MESSAGE_FLOW] ✅ Adding assistant message to state:", assistantMessage)
    setConversationMessages(prev => {
      console.log("[DEBUG_MESSAGE_FLOW] Previous messages count:", prev.length)
      const newMessages = [...prev, assistantMessage]
      console.log("[DEBUG_MESSAGE_FLOW] New messages count:", newMessages.length)
      return newMessages
    })
  } else {
    console.warn("[DEBUG_MESSAGE_FLOW] ⚠️ Message format not recognized!")
    console.warn("[DEBUG_MESSAGE_FLOW] Expected: string OR object with 'message' property")
    console.warn("[DEBUG_MESSAGE_FLOW] Received:", {
      type: typeof message,
      isObject: typeof message === "object",
      hasMessageProp: message && typeof message === "object" && 'message' in message,
      actualMessage: message
    })
  }
},
onToolCall: (toolCall: any) => {
  console.log("[TOOL_CALL] 🔧 Function tool called by agent:", {
    toolName: toolCall?.name,
    parameters: toolCall?.parameters,
    fullToolCall: toolCall
  })
  if (toolCall?.name === 'switch_to_script_tab' || toolCall?.name === 'load_script_for_rehearsal') {
    console.log("[TOOL_CALL] 🎬 Agent called script tab switching tool!")
    const isReady = toolCall.parameters?.ready === true || toolCall.parameters?.user_ready === true;
    if (isReady) {
      console.log("[TOOL_CALL] ✅ User confirmed ready - tool acknowledged (no automatic UI changes)")
      return {
        success: true,
        message: "User readiness confirmed. Please manually navigate to the Script tab to view your script."
      }
    } else {
      console.warn("[TOOL_CALL] ⚠️ Tool called but 'ready' parameter was not true:", toolCall.parameters)
      return {
        success: false,
        message: "User readiness was not confirmed in the tool parameters."
      }
    }
  }
  console.warn("[TOOL_CALL] ❓ Unknown tool called:", toolCall?.name)
  return {
    success: false,
    message: `Unknown tool: ${toolCall?.name || 'undefined'}`
  }
},
onModeChange: (mode: any) => {
  logConversationEvent("onModeChange", mode)
  console.log("[DEBUG_EVENTS] 🔄 Mode changed to:", mode)
  console.log("[DEBUG_EVENTS] 🔍 Mode change might indicate conversation activity")
},
onStatusChange: (status: any) => {
  logConversationEvent("onStatusChange", status)
  console.log("[DEBUG_EVENTS] 📊 Status changed to:", status)
  console.log("[DEBUG_EVENTS] 🔍 Status change might indicate conversation activity")

  // Check if this status change indicates the conversation is ready for messages
  if (status === 'connected' || status === 'listening' || status === 'speaking') {
    console.log("[DEBUG_EVENTS] ✅ Conversation appears to be active - onMessage should work now")
  }
},
onAudioPlay: () => {
  console.log("[DEBUG_EVENTS] 🔊 Audio playback started - AI is speaking")
  console.log("[DEBUG_EVENTS] 🔍 This indicates the conversation is active and should trigger onMessage")
},
onAudioStop: () => {
  console.log("[DEBUG_EVENTS] 🔇 Audio playback stopped - AI finished speaking")
  console.log("[DEBUG_EVENTS] 🔍 User can now speak and should trigger onMessage for voice input")
}
})

const { status: voiceStatus, isSpeaking } = conversation

// DEBUG: Test function to manually trigger onMessage handler
const testMessageHandler = (testMessage: any) => {
console.log("[DEBUG_MESSAGE_TEST] 🧪 Manually testing onMessage handler with:", testMessage)
try {
// Call the onMessage handler directly to test if it works
if (typeof testMessage === "string") {
console.log("[DEBUG_MESSAGE_TEST] Testing with string message:", testMessage)
const userMessage = {
id: uuidv4(),
type: "user" as const,
content: testMessage,
timestamp: new Date()
}
setConversationMessages(prev => {
console.log("[DEBUG_MESSAGE_TEST] Adding test user message to state")
return [...prev, userMessage]
})
} else if (testMessage && typeof testMessage === "object" && 'message' in testMessage) {
console.log("[DEBUG_MESSAGE_TEST] Testing with object message:", testMessage.message)
const assistantMessage = {
id: uuidv4(),
type: "assistant" as const,
content: testMessage.message,
timestamp: new Date()
}
setConversationMessages(prev => {
console.log("[DEBUG_MESSAGE_TEST] Adding test assistant message to state")
return [...prev, assistantMessage]
})
}
console.log("[DEBUG_MESSAGE_TEST] ✅ Test message handler completed successfully")
} catch (error) {
console.error("[DEBUG_MESSAGE_TEST] ❌ Error in test message handler:", error)
}
}

// Make test function available globally for debugging
useEffect(() => {
if (typeof window !== 'undefined') {
(window as any).testMessageHandler = testMessageHandler
console.log("[DEBUG_MESSAGE_TEST] 🌐 Test function available globally as window.testMessageHandler()")
}
}, [])

const extractCharacterNames = (script: string): string[] => {
const characterPattern = /^([A-Z][A-Z\s]+):/gm
const matches = script.match(characterPattern)
if (!matches) return []
return matches
.map(match => match.replace(':', '').trim())
.filter((char, index, arr) => arr.indexOf(char) === index)
.slice(0, 10)
}

const getVoiceDisplayName = (voiceId: string): string | null => {
const voice = AVAILABLE_VOICES.find(v => v.id === voiceId)
return voice ? voice.name : null
}

const updateAgentVoiceOnly = async (agentId: string, voiceId: string, apiKey: string) => {
const voiceName = getVoiceDisplayName(voiceId)
if (!voiceName) {
throw new Error(`Unknown voice ID: ${voiceId}`)
}
console.log(`[VOICE_SELECT] 🎤 Updating agent voice to: "${voiceName}" (preserving agent name)`)
const currentConfig = await getAgentConfiguration(agentId, apiKey)

// CRITICAL: NEVER change agent.name - it's the user-specific identifier (username-uuidv4)
// Only update the voice_id for TTS
const patchBody = {
  // DO NOT update name - preserve user-specific agent identifier
  conversation_config: {
    ...currentConfig.conversation_config,
    tts: {
      ...currentConfig.conversation_config?.tts,
      voice_id: voiceId // Only update the voice ID for TTS
    },
    agent: {
      ...currentConfig.conversation_config?.agent,
      // DO NOT update agent.name - preserve user-specific agent identifier
    }
  }
}

const client = createElevenLabsClient(apiKey)
const result = await client.conversationalAi.updateAgent(agentId, patchBody)
console.log(`[VOICE_SELECT] ✅ Agent voice updated to: "${voiceName}" (agent name preserved: "${currentConfig.name}")`)
return result
}

useEffect(() => {
const configureAgentForRehearsal = async () => {
if (isListening || !selectedVoiceId || !activeTab || !fileName || !agentName || !userAgentId) {
if (!userAgentId) setAgentConfigMessage('Setting up your personal agent...')
else if (!selectedVoiceId) setAgentConfigMessage('Please select a voice to prepare the agent.')
else if (!activeTab) setAgentConfigMessage('Please select a script to prepare the agent.')
else setAgentConfigState('idle')
return
}
console.log('[AGENT_CONFIG] 🚀 Starting background agent configuration...')
setAgentConfigState('updating')
setAgentConfigMessage(`Configuring ${agentName} for ${agentModality} mode...`)
try {
const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY
if (!apiKey) throw new Error("Missing ElevenLabs API Key")
console.log(`[AGENT_CONFIG] Updating agent prompt for modality: ${agentModality} (user agent: ${userAgentId})`)
await updateAgentPrompt(userAgentId, apiKey, {
scriptName: fileName,
agentName: agentName
})
console.log('[AGENT_CONFIG] ✅ Agent configuration successful!')
setAgentConfigState('ready')
setAgentConfigMessage(`${agentName} is ready for rehearsal.`)
} catch (error) {
console.error('[AGENT_CONFIG] ❌ Failed to configure agent:', error)
setAgentConfigState('error')
setAgentConfigMessage('Error preparing agent. Please try again.')
setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
}
}
configureAgentForRehearsal()
}, [agentModality, selectedVoiceId, activeTab, fileName, agentName, updateAgentPrompt, isListening, userAgentId])

const fetchAgentName = async () => {
try {
// Use user-specific agent ID if available, fallback to shared agent
const agentId = userAgentId || process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'
console.log(`[AGENT_MODALITY] 🔍 Fetching agent name for agent: ${agentId} (user-specific: ${!!userAgentId})`)
const agentConfig = await getAgentConfiguration(agentId, apiKey)
const extractedName = extractAgentName(agentConfig)
console.log(`[AGENT_MODALITY] ✅ Agent name fetched successfully: "${extractedName}"`)
setAgentName(extractedName)
} catch (error) {
console.warn(`[AGENT_MODALITY] ⚠️ Failed to fetch agent name, using fallback:`, error)
setAgentName('CastMate Assistant')
}
}

useEffect(() => {
if (userAgentId) {
fetchAgentName()
}
}, [userAgentId])

useEffect(() => {
if (selectedVoiceId && userAgentId) {
console.log(`[AGENT_MODALITY] 🔄 Voice changed to ${selectedVoiceId}, re-fetching agent name...`)
fetchAgentName()
}
}, [selectedVoiceId, userAgentId])

// Log agent architecture for debugging
useEffect(() => {
console.log("[AGENT_ARCHITECTURE] Current agent configuration:", {
conversationAgentId: effectiveAgentId,
userAgentId: userAgentId,
isUserSpecificAgent: !!userAgentId,
isUserAgentLoading: isUserAgentLoading,
userAgentError: userAgentError,
sharedAgentId: sharedAgentId,
apiKeyAvailable: !!process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY,
conversationStatus: conversation?.status,
isListening: isListening
})

console.log("[AGENT_ARCHITECTURE] 🏗️ Architecture Summary:")
if (userAgentId) {
  console.log(`[AGENT_ARCHITECTURE] 📞 Conversation: Using user agent (${userAgentId}) - PERSONALIZED`)
} else {
  console.log(`[AGENT_ARCHITECTURE] 📞 Conversation: Using shared agent (${sharedAgentId}) - FALLBACK`)
}
console.log("[AGENT_ARCHITECTURE] 💡 This progressive enhancement approach ensures the right voice is used.")
}, [userAgentId, effectiveAgentId, sharedAgentId, isUserAgentLoading, userAgentError, conversation?.status, isListening])


// =================================================================================
// ⭐️ FIXED: Step 2 of 2 - ensureAgentConfiguration now configures the user agent with tools
// This makes sure the primary agent is fully capable of handling the conversation.
// =================================================================================
useEffect(() => {
  if (isOpen) {
    const ensureAgentConfiguration = async () => {
      try {
        const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'

        // Step 1: Configure shared agent as a fallback
        console.log(`[AGENT_CONFIG] 🔧 Configuring shared agent as fallback: ${sharedAgentId}`)
        await configureAgentClientTools(sharedAgentId, apiKey)
        console.log('[AGENT_CONFIG] ✅ Shared agent (fallback) configured.')

        // Step 2: If user agent is available, configure it as the primary agent
        if (userAgentId) {
          console.log(`[AGENT_CONFIG] 🎤 Configuring primary user agent for conversation: ${userAgentId}`)
          // THIS IS THE KEY ADDITION: Configure tools on the user agent
          await configureAgentClientTools(userAgentId, apiKey)

          const userAgentConfig = await getAgentConfiguration(userAgentId, apiKey)
          console.log('[AGENT_CONFIG] 📋 User agent ready for conversation:', {
            agentId: userAgentId,
            agentName: userAgentConfig.name,
          })
        }

        console.log('[AGENT_CONFIG] ✅ Agent configuration completed successfully')
      } catch (error) {
        console.warn('[AGENT_CONFIG] ⚠️ Failed to configure agents:', error)
        setDetailedErrorInfo(`Agent configuration failed: ${error instanceof Error ? error.message : String(error)}`)
      }
    }
    ensureAgentConfiguration()
  }
}, [isOpen, sharedAgentId, userAgentId])

// CRITICAL: Validate agent configuration for text response transmission
useEffect(() => {
  if (userAgentId && !isUserAgentLoading && !isValidatingAgentConfig) {
    console.log(`[AGENT_CONFIG_VALIDATION] 🔍 Validating configuration for user agent: ${userAgentId}`);
    validateAgentConfiguration();
  }
}, [userAgentId, isUserAgentLoading, validateAgentConfiguration, isValidatingAgentConfig]);

// Log agent configuration validation results
useEffect(() => {
  if (agentConfigValidationResult) {
    if (agentConfigValidationResult.success) {
      if (agentConfigValidationResult.configurationFixed) {
        console.log(`[AGENT_CONFIG_VALIDATION] ✅ Configuration was fixed for agent ${agentConfigValidationResult.agentId}`);
        console.log(`[AGENT_CONFIG_VALIDATION] Details: ${agentConfigValidationResult.details}`);
      } else {
        console.log(`[AGENT_CONFIG_VALIDATION] ✅ Configuration was already correct for agent ${agentConfigValidationResult.agentId}`);
      }
    } else {
      console.error(`[AGENT_CONFIG_VALIDATION] ❌ Configuration validation failed:`, agentConfigValidationResult.error);
      setDetailedErrorInfo(`Agent configuration issue: ${agentConfigValidationResult.error}`);
    }
  }
}, [agentConfigValidationResult]);

useEffect(() => {
if (fileId) {
setActiveTab(fileId)
}
}, [fileId])

useEffect(() => {
let interval: NodeJS.Timeout | null = null
if (sessionStartTime && isListening) {
interval = setInterval(() => {
const now = new Date()
const duration = Math.floor((now.getTime() - sessionStartTime.getTime()) / 1000)
setSessionDuration(duration)
}, 1000)
}
return () => {
if (interval) {
clearInterval(interval)
}
}
}, [sessionStartTime, isListening])

const { namespace, fileName: namespaceFileName } = useGetNamespace(userId, activeTab || null)

useEffect(() => {
if (namespaceFileName) {
setFileName(namespaceFileName)
}
}, [namespaceFileName])

useEffect(() => {
if (status === StatusText.ERROR && uploadError) {
setError(`Upload error: ${uploadError}`)
setIsUploading(false)
setUploadProgress(null)
} else if (status === StatusText.UPLOADING) {
setIsUploading(true)
setUploadProgress(progress || 0)
setUploadStatusText("Uploading script...")
} else if (status === StatusText.PROCESSING) {
setIsUploading(true)
setUploadProgress(progress || 0)
setUploadStatusText("Processing script...")
} else if (status === StatusText.COMPLETED) {
setIsUploading(false)
setUploadProgress(null)
fetchScriptFiles()
}
}, [status, progress, uploadError])

const debugAudioSettings = async () => {
console.log("[AUDIO_DEBUG] Checking browser audio capabilities...")
try {
const AudioContext = window.AudioContext || (window as any).webkitAudioContext
if (!AudioContext) {
console.error("[AUDIO_DEBUG] AudioContext not supported")
return false
}
if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
const devices = await navigator.mediaDevices.enumerateDevices()
const audioOutputs = devices.filter(device => device.kind === 'audiooutput')
const audioInputs = devices.filter(device => device.kind === 'audioinput')
console.log("[AUDIO_DEBUG] Audio devices:", {
outputs: audioOutputs.length,
inputs: audioInputs.length,
outputDevices: audioOutputs.map(d => ({ id: d.deviceId, label: d.label })),
inputDevices: audioInputs.map(d => ({ id: d.deviceId, label: d.label }))
})
}
console.log("[AUDIO_DEBUG] Current audio state:", {
isMuted: isMuted,
conversationStatus: voiceStatus,
isSpeaking: isSpeaking,
isListening: isListening
})
return true
} catch (error) {
console.error("[AUDIO_DEBUG] Audio debug failed:", error)
return false
}
}

const requestCameraPermission = async () => {
try {
console.log("[VIDEO] Requesting camera and microphone permission...")
const stream = await navigator.mediaDevices.getUserMedia({
video: { width: 1280, height: 720 },
audio: true
})
setHasCameraPermission(true)
setRecordingError(null)
console.log("[VIDEO] Camera and microphone permission granted")
stream.getTracks().forEach(track => track.stop())
} catch (error) {
setRecordingError("Camera access denied - please enable in browser settings")
console.error("[VIDEO] Error accessing camera:", error)
}
}

useEffect(() => {
const requestMicPermission = async () => {
try {
console.log("[AUDIO] Requesting microphone permission...")
const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
setHasPermission(true)
setVoiceErrorMessage("")
console.log("[AUDIO] Microphone permission granted")
stream.getTracks().forEach(track => track.stop())
await debugAudioSettings()
} catch (error) {
setVoiceErrorMessage("Microphone access denied - please enable in browser settings")
console.error("[AUDIO] Error accessing microphone:", error)
}
}
if (activeSection === "rehearsing") {
requestMicPermission()
if (recordingMode === 'video') {
requestCameraPermission()
}
}
}, [activeSection, recordingMode])

useEffect(() => {
return () => {
if (recordingTimerRef.current) {
clearInterval(recordingTimerRef.current)
}
if (currentStream) {
currentStream.getTracks().forEach(track => track.stop())
}
}
}, [currentStream])

const handleVoiceSelect = async (voiceId: string) => {
console.log(`[VOICE_SELECT] Starting voice selection for: ${voiceId}`)
if (isUpdatingVoice) return

// Ensure we have a user agent ID
if (!userAgentId) {
  setVoiceErrorMessage("Personal agent not available. Please wait for setup to complete.")
  console.error("[VOICE_SELECT] No user agent ID available")
  return
}

const voiceName = getVoiceDisplayName(voiceId)
if (!voiceName) {
  setVoiceErrorMessage("Unknown voice selected.")
  console.error("[VOICE_SELECT] Unknown voice ID:", voiceId)
  return
}

setSelectedVoiceId(voiceId)
setIsUpdatingVoice(true)
setAgentConfigState('updating')
setAgentConfigMessage(`Updating voice to ${voiceName}...`)

try {
  const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY
  if (!apiKey) throw new Error("Missing ElevenLabs API key.")

  // Step 1: Update agent voice (preserving agent name)
  console.log(`[VOICE_SELECT] Step 1: Updating agent voice to "${voiceName}" (preserving agent identifier)`)
  await updateAgentVoiceOnly(userAgentId, voiceId, apiKey)

  // Step 2: Update agent prompt with new conversational identity
  console.log(`[VOICE_SELECT] Step 2: Updating agent prompt with new conversational identity`)
  await updateAgentPrompt(userAgentId, apiKey, {
    scriptName: fileName,
    agentName: voiceName // Use the voice name as the conversational identity in prompt
  })

  // Step 3: Update local state for UI display
  console.log(`[VOICE_SELECT] Step 3: Updating local state for UI display`)
  setAgentName(voiceName)

  console.log(`[VOICE_SELECT] ✅ Complete voice and identity update successful: ${voiceName}`)
  setAgentConfigState('ready')
  setAgentConfigMessage(`${voiceName} is ready for rehearsal.`)
} catch (error) {
  console.error('[VOICE_SELECT] Error updating agent voice and identity:', error)
  setAgentConfigState('error')
  setAgentConfigMessage('Failed to update voice.')
  setVoiceErrorMessage(error instanceof Error ? error.message : "Voice update failed.")
} finally {
  setIsUpdatingVoice(false)
}
}

const handleStartConversation = async () => {
console.log("[CONVERSATION_START] Attempting to start streamlined conversation.")

// Check user agent availability first
if (isUserAgentLoading) {
  setVoiceErrorMessage("Setting up your personal agent. Please wait...")
  console.warn("[CONVERSATION_START] User agent is still loading")
  return
}

if (userAgentError) {
  setVoiceErrorMessage(`Failed to set up your personal agent: ${userAgentError}`)
  console.error("[CONVERSATION_START] User agent error:", userAgentError)
  return
}

if (!userAgentId) {
  setVoiceErrorMessage("Unable to create your personal agent. Please try refreshing the page.")
  console.error("[CONVERSATION_START] No user agent ID available")
  return
}

if (agentConfigState !== 'ready') {
  setVoiceErrorMessage("Agent is not ready. Please wait for configuration to complete.")
  console.warn(`[CONVERSATION_START] Blocked. Agent state is: ${agentConfigState}`)
  return
}
if (!hasPermission) {
  setVoiceErrorMessage("Microphone access is required.")
  return
}
if (!session?.user?.email) {
  setVoiceErrorMessage("You must be signed in.")
  return
}

// Validate script selection and prepare session parameters
if (!activeTab) {
  setVoiceErrorMessage("Please select a script before starting rehearsal.")
  console.warn("[CONVERSATION_START] No script selected (activeTab is null)")
  return
}

if (!fileName) {
  setVoiceErrorMessage("Script name not available. Please try selecting the script again.")
  console.warn("[CONVERSATION_START] Script name not available (fileName is null)")
  return
}

// Check script validation status
if (scriptValidationStatus.isValidating) {
  setVoiceErrorMessage("Script validation in progress. Please wait...")
  console.warn("[CONVERSATION_START] Script validation still in progress")
  return
}

if (scriptValidationStatus.isSynchronizing) {
  setVoiceErrorMessage("Script synchronization in progress. Please wait...")
  console.warn("[CONVERSATION_START] Script synchronization still in progress")
  return
}

if (!scriptValidationStatus.isReady) {
  const errorMsg = scriptValidationStatus.error
    ? `Script validation failed: ${scriptValidationStatus.error}`
    : "Script is not ready for rehearsal. Please try selecting the script again."
  setVoiceErrorMessage(errorMsg)
  console.warn("[CONVERSATION_START] Script not ready for rehearsal:", scriptValidationStatus)
  return
}

try {
  setApiConfigStatus('connecting')
  setIsListening(true)
  setVoiceErrorMessage("Connecting to rehearsal session...")

  // Prepare session parameters with script context
  const sessionParams: any = {
    scriptId: activeTab,
    scriptName: fileName,
    documentName: fileName // ElevenLabs may use this to identify the knowledge base document
  }

  console.log("[CONVERSATION_START] Starting session with script context:", {
    scriptId: activeTab,
    scriptName: fileName,
    agentId: effectiveAgentId, // The agent we are connecting to
    isUserSpecificAgent: !!userAgentId,
    userEmail: session?.user?.email,
    scriptValidationStatus: scriptValidationStatus
  })

  console.log("[MESSAGE_FLOW_CONFIG] 🔍 Verifying agent architecture:", {
    conversationAgent: effectiveAgentId,
    userAgentId: userAgentId,
    hasUserAgent: !!userAgentId,
    agentConfigState: agentConfigState,
    selectedVoiceId: selectedVoiceId,
    agentName: agentName
  })

  console.log("[MESSAGE_FLOW_CONFIG] 🏗️ Architecture Status:")
  if (userAgentId) {
    console.log(`[MESSAGE_FLOW_CONFIG] 📞 Conversation: Using primary user agent (${userAgentId}) - PERSONALIZED VOICE`)
  } else {
    console.log(`[MESSAGE_FLOW_CONFIG] 📞 Conversation: Using fallback shared agent (${sharedAgentId}) - DEFAULT VOICE`)
  }

  console.log("[DEBUG_MESSAGE_FLOW] 🚀 About to start session - onMessage handler should be ready")

  // Test if the conversation object has the expected methods and properties
  console.log("[DEBUG_MESSAGE_FLOW] 🔍 Conversation object inspection:", {
    hasStartSession: typeof conversation.startSession === 'function',
    hasEndSession: typeof conversation.endSession === 'function',
    hasSetVolume: typeof conversation.setVolume === 'function',
    conversationStatus: conversation.status,
    conversationKeys: Object.keys(conversation)
  })

  await conversation.startSession(sessionParams)

  console.log("[CONVERSATION_START] ✅ Streamlined conversation started successfully with script:", fileName)
  console.log("[DEBUG_MESSAGE_FLOW] 🎯 Session started - onMessage handler should now receive messages")
  console.log("[DEBUG_MESSAGE_FLOW] 🔍 Post-session conversation status:", conversation.status)

  // CRITICAL: Verify final architecture after session start
  console.log("[MESSAGE_FLOW_VERIFICATION] 🔍 Final verification after session start:", {
    sessionActive: !!conversation.status,
    conversationStatus: conversation.status,
    conversationAgent: effectiveAgentId,
    userAgentId: userAgentId,
    hasUserAgent: !!userAgentId,
    messageHandlerReady: conversation.status === 'connected',
    conversationMessagesCount: conversationMessages.length,
    agentName: agentName,
    selectedVoiceId: selectedVoiceId
  })

  console.log("[MESSAGE_FLOW_VERIFICATION] ✅ SUCCESS: Unified architecture active")
  if (userAgentId) {
    console.log(`[MESSAGE_FLOW_VERIFICATION] 🎤 Voice & Message Flow: User agent (${userAgentId}) - PERSONALIZED`)
  } else {
    console.log(`[MESSAGE_FLOW_VERIFICATION] 🎤 Voice & Message Flow: Shared agent (${sharedAgentId}) - FALLBACK`)
  }

  // Run comprehensive debugging to help diagnose message capture issues
  debugMessageCaptureSetup()

  setApiConfigStatus('valid')
  setVoiceErrorMessage("")
} catch (error) {
  console.error("[CONVERSATION_START] ❌ Error starting streamlined conversation:", error)
  setVoiceErrorMessage("Failed to start conversation. Please try again.")
  setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
  setIsListening(false)
  setApiConfigStatus('invalid')
}
}

const handleEndConversation = async () => {
try {
setIsListening(false)
await conversation.endSession()
} catch (error) {
console.error("Error ending conversation:", error)
setVoiceErrorMessage("Failed to end conversation")
setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
}
}

const toggleMute = async () => {
try {
console.log("[AUDIO] Toggling mute. Current state:", { isMuted, voiceStatus })
if (voiceStatus !== 'connected') {
console.warn("[AUDIO] Cannot change volume - conversation not connected")
setVoiceErrorMessage("Start a conversation first to control volume")
return
}
const newVolume = isMuted ? 1 : 0
console.log("[AUDIO] Setting volume to:", newVolume)
if (conversation.setVolume) {
conversation.setVolume({ volume: newVolume })
setIsMuted(!isMuted)
console.log("[AUDIO] Volume changed successfully to:", newVolume)
} else {
console.warn("[AUDIO] setVolume method not available on conversation object")
setVoiceErrorMessage("Volume control not available")
}
} catch (error) {
console.error("[AUDIO] Error changing volume:", error)
setVoiceErrorMessage("Failed to change volume")
setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
}
}

const fetchMostRecentFile = async (): Promise<{id: string, namespace: string, name: string} | null> => {
if (!userId) return null
try {
const filesRef = collection(db, `users/${userId}/files`)
const q = query(filesRef, orderBy("createdAt", "desc"), limit(1))
const querySnapshot = await getDocs(q)
if (!querySnapshot.empty) {
const fileDoc = querySnapshot.docs[0]
const fileData = fileDoc.data()
return {
id: fileDoc.id,
namespace: fileData.namespace || fileDoc.id,
name: fileData.name || "Untitled Document"
}
}
return null
} catch (err) {
console.error("Error fetching most recent file:", err)
return null
}
}

const fetchScriptFiles = async () => {
if (sessionStatus === "loading") {
return
}
if (!session?.user?.email) {
setError("Please sign in to access your scripts.")
setLoading(false)
return
}
try {
const db = getFirestore()
const filesRef = collection(db, `users/${session.user.email}/files`)
const q = query(
filesRef,
where("category", "==", "SceneMate"),
orderBy("name", "asc")
)
const querySnapshot = await getDocs(q)
const files: ScriptFile[] = []
querySnapshot.forEach((doc) => {
const fileData = doc.data()
files.push({
id: doc.id,
name: fileData.name || "Untitled Script",
namespace: fileData.namespace || doc.id
})
})
files.sort((a, b) => a.name.localeCompare(b.name))
setScriptFiles(files)
if (!activeTab && files.length > 0) {
setActiveTab(files[0].id)
}
setLoading(false)
} catch (err) {
console.error("Error fetching script files:", err)
setError(`Failed to load scripts: ${err instanceof Error ? err.message : "Unknown error"}`)
setLoading(false)
}
}

useEffect(() => {
if (sessionStatus === "authenticated") {
fetchScriptFiles()
}
}, [sessionStatus, userId, activeTab])

const fetchFileDocumentId = async (namespace: string) => {
if (!userId) return
try {
const filesRef = collection(db, `users/${userId}/files`)
const q = query(filesRef, where("namespace", "==", namespace), limit(1))
const querySnapshot = await getDocs(q)
if (!querySnapshot.empty) {
const fileDoc = querySnapshot.docs[0]
setFileDocumentId(fileDoc.id)
const fileData = fileDoc.data()
if (fileData.name) {
setFileName(fileData.name)
}
}
} catch (err) {
console.error("Error fetching file document ID:", err)
}
}

/**

Validates script availability in both Firebase and ElevenLabs knowledge base

Triggers automatic synchronization if script exists in Firebase but not in ElevenLabs
*/
const validateAndSyncScript = async (scriptId: string, scriptName: string, scriptNamespace: string) => {
if (!userAgentId) {
console.warn("[SCRIPT_VALIDATION] No user agent ID available, skipping validation")
return
}

console.log("[SCRIPT_VALIDATION] 🔍 Starting dual validation for script:", {
  scriptId,
  scriptName,
  scriptNamespace,
  agentId: userAgentId
})

setScriptValidationStatus({
  isValidating: true,
  isSynchronizing: false,
  firebaseExists: false,
  elevenLabsExists: false,
  isReady: false,
  statusMessage: "Verifying script availability..."
})

try {
  // Step 1: Validate Firebase existence and fetch chunks
  console.log("[SCRIPT_VALIDATION] Step 1: Validating Firebase existence...")
  const fileDocRef = doc(db, `users/${userId}/files/${scriptId}`)
  const fileDocSnap = await getDoc(fileDocRef)

  if (!fileDocSnap.exists()) {
    setScriptValidationStatus({
      isValidating: false,
      isSynchronizing: false,
      firebaseExists: false,
      elevenLabsExists: false,
      isReady: false,
      statusMessage: "Script not found in Firebase",
      error: "Script file not found"
    })
    return
  }

  const fileData = fileDocSnap.data()
  const fileNamespace = fileData.namespace || scriptId
  const chunksRef = collection(db, `users/${userId}/byteStoreCollection`)
  const q = query(chunksRef, where("metadata.doc_id", "==", fileNamespace))
  const querySnapshot = await getDocs(q)
  const chunks = querySnapshot.docs.map((d) => d.data())

  if (chunks.length === 0) {
    setScriptValidationStatus({
      isValidating: false,
      isSynchronizing: false,
      firebaseExists: false,
      elevenLabsExists: false,
      isReady: false,
      statusMessage: "No script content found in Firebase",
      error: "No content found for this script"
    })
    return
  }

  console.log("[SCRIPT_VALIDATION] ✅ Firebase validation successful:", {
    chunks: chunks.length,
    namespace: fileNamespace
  })

  setScriptValidationStatus(prev => ({
    ...prev,
    firebaseExists: true,
    statusMessage: "Checking ElevenLabs knowledge base..."
  }))

  // Step 2: Check ElevenLabs knowledge base
  console.log("[SCRIPT_VALIDATION] Step 2: Checking ElevenLabs knowledge base...")
  const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'

  const elevenLabsCheck = await checkScriptInAgentKnowledgeBase(
    userAgentId,
    scriptName,
    scriptNamespace,
    apiKey
  )

  if (elevenLabsCheck.exists) {
    console.log("[SCRIPT_VALIDATION] ✅ Script found in ElevenLabs knowledge base:", elevenLabsCheck.matchType)
    setScriptValidationStatus({
      isValidating: false,
      isSynchronizing: false,
      firebaseExists: true,
      elevenLabsExists: true,
      isReady: true,
      statusMessage: "Script ready for rehearsal"
    })
  } else {
    console.log("[SCRIPT_VALIDATION] ❌ Script not found in ElevenLabs, starting synchronization...")

    setScriptValidationStatus(prev => ({
      ...prev,
      elevenLabsExists: false,
      isSynchronizing: true,
      statusMessage: "Syncing script to rehearsal system..."
    }))

    // Step 3: Synchronize script to ElevenLabs
    console.log("[SCRIPT_VALIDATION] Step 3: Synchronizing script to ElevenLabs...")
    const syncResult = await synchronizeScriptToAgent(
      chunks,
      scriptName,
      scriptNamespace,
      userAgentId,
      apiKey
    )

    if (syncResult.success) {
      console.log("[SCRIPT_VALIDATION] ✅ Script synchronization successful")
      setScriptValidationStatus({
        isValidating: false,
        isSynchronizing: false,
        firebaseExists: true,
        elevenLabsExists: true,
        isReady: true,
        statusMessage: "Script synchronized and ready for rehearsal"
      })
    } else {
      console.error("[SCRIPT_VALIDATION] ❌ Script synchronization failed:", syncResult.error)
      setScriptValidationStatus({
        isValidating: false,
        isSynchronizing: false,
        firebaseExists: true,
        elevenLabsExists: false,
        isReady: false,
        statusMessage: "Failed to sync script to rehearsal system",
        error: syncResult.error
      })
    }
  }

} catch (error) {
  console.error("[SCRIPT_VALIDATION] ❌ Script validation failed:", error)
  setScriptValidationStatus({
    isValidating: false,
    isSynchronizing: false,
    firebaseExists: false,
    elevenLabsExists: false,
    isReady: false,
    statusMessage: "Script validation failed",
    error: error instanceof Error ? error.message : String(error)
  })
}
}

const fetchScriptContent = async () => {
if (!activeTab || !userId) {
setError("No script selected or user not authenticated")
return
}
setIsScriptLoading(true)
setError(null)
try {
const fileDocRef = doc(db, `users/${userId}/files/${activeTab}`)
const fileDocSnap = await getDoc(fileDocRef)
if (!fileDocSnap.exists()) {
setError("Script file not found")
setIsScriptLoading(false)
return
}
const fileData = fileDocSnap.data()
const fileNamespace = fileData.namespace || activeTab
const chunksRef = collection(db, `users/${userId}/byteStoreCollection`)
const q = query(chunksRef, where("metadata.doc_id", "==", fileNamespace))
const querySnapshot = await getDocs(q)
const chunks = querySnapshot.docs.map((d) => d.data())
if (chunks.length === 0) {
setError("No content found for this script")
setIsScriptLoading(false)
return
}
if ("position" in chunks[0]) {
chunks.sort((a, b) => (a.position || 0) - (b.position || 0))
} else if ("metadata" in chunks[0] && "page_number" in chunks[0].metadata) {
chunks.sort((a, b) => (a.metadata.page_number || 0) - (b.metadata.page_number || 0))
}
const contentField = "pageContent" in chunks[0] ? "pageContent" : "content"
const content = chunks.map((chunk) => chunk[contentField] || "").join("\n")
setScriptContent(content)
setIsScriptReady(true)
} catch (err) {
console.error("Error fetching script content:", err)
setError("Failed to load script content")
setIsScriptLoading(false)
} finally {
setIsScriptLoading(false)
}
}

const createNewChat = async (fileNamespace?: string, fileDocId?: string) => {
if (!userId) {
setError("Please sign in to create a new chat.")
return null
}
try {
let firstMessageText = "New Rehearsal"
const chatsRef = collection(db, `users/${userId}/chats`)
let actualFileDocId: string
let actualNamespace: string
if (fileNamespace && fileDocId) {
actualNamespace = fileNamespace
actualFileDocId = fileDocId
} else if (fileDocId) {
actualNamespace = fileDocId
actualFileDocId = fileDocId
} else if (fileNamespace) {
actualNamespace = fileNamespace
actualFileDocId = fileNamespace
} else {
const recentFile = await fetchMostRecentFile()
if (recentFile) {
actualNamespace = recentFile.namespace
actualFileDocId = recentFile.id
setFileName(recentFile.name)
} else {
setError("Please upload or select a file before creating a chat.")
return null
}
}
const chatData = {
createdAt: serverTimestamp(),
userId: userId,
firstMessage: firstMessageText,
lastUpdated: serverTimestamp(),
fileNamespace: actualNamespace,
fileDocumentId: actualFileDocId
}
const docRef = await addDoc(chatsRef, chatData)
const newChatId = docRef.id
setChatId(newChatId)
setChatMessages([])
setSelectedFileNamespace(actualNamespace)
setFileDocumentId(actualFileDocId)
return newChatId
} catch (err) {
if (isMounted.current) {
setError(
"Failed to create new chat: " +
(err instanceof Error ? err.message : "Unknown error")
)
}
return null
}
}

const handleFileUpload = useCallback(
async (event: React.ChangeEvent<HTMLInputElement>) => {
const file = event.target.files?.[0]
if (!file || !userId) {
setError("No file selected or user not authenticated.")
return
}
try {
const docId = uuidv4()
await handleUpload(file, null, userId, docId)
const newChatId = await createNewChat(docId, docId)
if (newChatId) {
setSelectedFileNamespace(docId)
setFileDocumentId(docId)
setFileName(file.name)
setChatId(newChatId)
const messagesRef = collection(db, `users/${userId}/chats/${newChatId}/messages`)
const welcomeMessageData = {
role: "assistant",
text: "File processed successfully! How can I assist with your script?",
createdAt: serverTimestamp(),
fileDocumentId: docId
}
const welcomeDocRef = await addDoc(messagesRef, welcomeMessageData)
const initialMessage: ChatMessage = {
id: welcomeDocRef.id,
role: "assistant",
content: welcomeMessageData.text,
timestamp: new Date().toISOString(),
fileDocumentId: docId
}
setChatMessages([initialMessage])
}
} catch (err) {
setError(err instanceof Error ? err.message : "Upload failed")
}
},
[userId, handleUpload, createNewChat]
)

const handleUploadClick = () => {
if (fileInputRef.current) {
fileInputRef.current.click()
}
}

const handleScriptDeleted = useCallback(async () => {
console.log("Script deleted, updating UI state...")
const deletedScriptId = activeTab
setActiveTab(null)
setFileName(null)
setScriptContent("")
setIsScriptLoading(false)
setIsScriptReady(false)
setIsFormatting(false)
setFormattedMarkdown("")
setError(null)
try {
const db = getFirestore()
const filesRef = collection(db, `users/${session?.user?.email}/files`)
const q = query(
filesRef,
where("category", "==", "SceneMate"),
orderBy("name", "asc")
)
const querySnapshot = await getDocs(q)
const files: ScriptFile[] = []
querySnapshot.forEach((doc) => {
const fileData = doc.data()
files.push({
id: doc.id,
name: fileData.name || "Untitled Script",
namespace: fileData.namespace || doc.id
})
})
files.sort((a, b) => a.name.localeCompare(b.name))
setScriptFiles(files)
if (files.length > 0) {
console.log(`${files.length} scripts remaining after deletion`)
setActiveTab(null)
} else {
console.log("No scripts remaining after deletion")
setActiveTab(null)
}
setLoading(false)
} catch (err) {
console.error("Error refreshing script files after deletion:", err)
setError(`Failed to refresh scripts: ${err instanceof Error ? err.message : "Unknown error"}`)
setLoading(false)
}
}, [activeTab, session?.user?.email])

const startSelfTakeRecording = async () => {
if (!userId) {
setRecordingError('Please sign in to record')
return
}
try {
setRecordingError(null)
setRecordingDuration(0)
let stream: MediaStream
let mimeType: string
if (recordingMode === 'video') {
stream = await navigator.mediaDevices.getUserMedia({
video: {
width: { ideal: 1280 },
height: { ideal: 720 },
frameRate: { ideal: 30 }
},
audio: true
})
if (MediaRecorder.isTypeSupported('video/webm;codecs=vp9,opus')) {
mimeType = 'video/webm;codecs=vp9,opus'
} else if (MediaRecorder.isTypeSupported('video/webm')) {
mimeType = 'video/webm'
} else if (MediaRecorder.isTypeSupported('video/mp4')) {
mimeType = 'video/mp4'
} else {
throw new Error('No supported video format found')
}
} else {
stream = await navigator.mediaDevices.getUserMedia({ audio: true })
mimeType = 'audio/webm;codecs=opus'
}
setCurrentStream(stream)
const recorder = new MediaRecorder(stream, { mimeType })
mediaRecorderRef.current = recorder
const chunks: Blob[] = []
recorder.ondataavailable = (e) => chunks.push(e.data)
recorder.onstop = () => {
const blob = new Blob(chunks, { type: mimeType })
uploadSelfTakeRecording(blob)
stream.getTracks().forEach((track) => track.stop())
setCurrentStream(null)
}
recordingTimerRef.current = setInterval(() => {
setRecordingDuration(prev => prev + 1)
}, 1000)
recorder.start()
setIsRecording(true)
console.log(`[SelfTake] Started ${recordingMode} recording with format: ${mimeType}`)
} catch (error) {
setRecordingError(`Failed to start ${recordingMode} recording. Please check your permissions.`)
console.error('Recording error:', error)
}
}

const stopSelfTakeRecording = () => {
if (mediaRecorderRef.current && isRecording) {
mediaRecorderRef.current.stop()
setIsRecording(false)
if (recordingTimerRef.current) {
clearInterval(recordingTimerRef.current)
recordingTimerRef.current = null
}
}
}

const uploadSelfTakeRecording = async (blob: Blob) => {
if (!userId) return
try {
const formData = new FormData()
const rehearsalId = `rehearsal-${Date.now()}`
const isVideo = recordingMode === 'video'
const fileExtension = isVideo ? (blob.type.includes('webm') ? 'webm' : 'mp4') : 'mp3'
const filename = `selftake-${rehearsalId}.${fileExtension}`
const apiEndpoint = isVideo ? "/api/selfTakeVideo" : "/api/selfTakeAudio"
const fieldName = isVideo ? "video" : "audio"
formData.append(fieldName, blob, filename)
formData.append("userId", userId)
formData.append("rehearsalId", rehearsalId)
formData.append("scriptName", fileName || "Unknown Script")
formData.append("duration", recordingDuration.toString())
const response = await fetch(apiEndpoint, {
method: "POST",
body: formData,
})
if (response.ok) {
const data = await response.json()
const newRecording = {
id: data.id,
filename: data.filename,
url: data.url,
timestamp: new Date(data.timestamp),
rehearsalId: rehearsalId,
type: recordingMode,
duration: data.duration || recordingDuration,
fileSize: data.fileSize
}
setRecordings(prev => [newRecording, ...prev])
setRecordingError(null)
console.log(`[SelfTake] ${recordingMode} recording uploaded successfully:`, newRecording)
} else {
const errorData = await response.json()
setRecordingError(errorData.error || `Failed to save ${recordingMode} recording`)
}
} catch (error) {
console.error('Upload error:', error)
setRecordingError(`Failed to upload ${recordingMode} recording`)
}
}

const toggleRecordingPlayback = (recording: any) => {
const audioKey = recording.id
if (playingRecording === audioKey) {
if (audioElements[audioKey]) {
audioElements[audioKey].pause()
audioElements[audioKey].currentTime = 0
}
setPlayingRecording(null)
} else {
Object.values(audioElements).forEach(audio => {
audio.pause()
audio.currentTime = 0
})
setPlayingRecording(null)
if (!audioElements[audioKey]) {
const audio = new Audio(recording.url)
audio.onended = () => setPlayingRecording(null)
audio.onerror = () => {
setRecordingError('Failed to play recording')
setPlayingRecording(null)
}
setAudioElements(prev => ({ ...prev, [audioKey]: audio }))
audio.play().then(() => setPlayingRecording(audioKey))
} else {
audioElements[audioKey].currentTime = 0
audioElements[audioKey].play().then(() => setPlayingRecording(audioKey))
}
}
}

const deleteRecording = async (recording: any) => {
if (!userId) return
const recordingType = recording.type || 'audio'
if (!confirm(`Delete ${recordingType} recording from ${recording.timestamp.toLocaleString()}?`)) {
return
}
try {
const apiEndpoint = recordingType === 'video' ? "/api/selfTakeVideo" : "/api/selfTakeAudio"
const response = await fetch(`${apiEndpoint}?id=${encodeURIComponent(recording.id)}&userId=${encodeURIComponent(userId)}`, {
method: "DELETE",
})
if (response.ok) {
setRecordings(prev => prev.filter(r => r.id !== recording.id))
if (playingRecording === recording.id) {
if (audioElements[recording.id]) {
audioElements[recording.id].pause()
}
setPlayingRecording(null)
}
setAudioElements(prev => {
const newElements = { ...prev }
delete newElements[recording.id]
return newElements
})
console.log(`[SelfTake] ${recordingType} recording deleted successfully:`, recording.id)
} else {
const errorData = await response.json()
setRecordingError(errorData.error || `Failed to delete ${recordingType} recording`)
}
} catch (error) {
console.error('Delete error:', error)
setRecordingError(`Failed to delete ${recordingType} recording`)
}
}

const loadAllRecordings = async () => {
if (!userId) return
try {
const audioResponse = await fetch(`/api/selfTakeAudio?userId=${encodeURIComponent(userId)}`)
const audioData = audioResponse.ok ? await audioResponse.json() : { recordings: [] }
const videoResponse = await fetch(`/api/selfTakeVideo?userId=${encodeURIComponent(userId)}`)
const videoData = videoResponse.ok ? await videoResponse.json() : { recordings: [] }
const allRecordings = [
...(audioData.recordings || []).map((r: any) => ({ ...r, type: 'audio' })),
...(videoData.recordings || []).map((r: any) => ({ ...r, type: 'video' }))
].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
setRecordings(allRecordings)
console.log('[SelfTake] Loaded recordings:', {
audio: audioData.recordings?.length || 0,
video: videoData.recordings?.length || 0,
total: allRecordings.length
})
} catch (error) {
console.error('Error loading recordings:', error)
setRecordingError('Failed to load recordings')
}
}

// Enhanced script selection handler with validation
const handleScriptSelection = async (scriptId: string) => {
console.log("[SCRIPT_SELECTION] 📝 Script selected:", scriptId)

setActiveTab(scriptId)

// Find script details
const selectedScript = scriptFiles.find(file => file.id === scriptId)
if (!selectedScript) {
  console.warn("[SCRIPT_SELECTION] Selected script not found in scriptFiles")
  return
}

console.log("[SCRIPT_SELECTION] Script details:", {
  id: selectedScript.id,
  name: selectedScript.name,
  namespace: selectedScript.namespace
})

// Trigger dual validation and synchronization
if (userAgentId) {
  await validateAndSyncScript(scriptId, selectedScript.name, selectedScript.namespace)
} else {
  console.warn("[SCRIPT_SELECTION] No user agent ID available, skipping validation")
}
}

useEffect(() => {
if (activeSection === "script" && activeTab) {
fetchScriptContent()
}
}, [activeSection, activeTab])

// Trigger validation when script is selected and user agent becomes available
useEffect(() => {
if (activeTab && userAgentId && !scriptValidationStatus.isValidating && !scriptValidationStatus.isReady) {
const selectedScript = scriptFiles.find(file => file.id === activeTab)
if (selectedScript) {
console.log("[SCRIPT_VALIDATION] User agent available, triggering validation for:", selectedScript.name)
validateAndSyncScript(activeTab, selectedScript.name, selectedScript.namespace)
}
}
}, [activeTab, userAgentId, scriptFiles, scriptValidationStatus.isReady, scriptValidationStatus.isValidating]) // Added dependencies for safety

useEffect(() => {
if (isScriptReady && scriptContent && !isFormatting) {
const formatScriptContent = async () => {
setIsFormatting(true)
try {
const formattedScript = {
metadata: { title: fileName || "Untitled", author: "", characters: [], summary: "" },
lines: scriptContent.split("\n").map((line, idx) => ({
lineNumber: idx + 1,
text: line,
})),
}
setFormattedMarkdown(scriptContent)
} catch (err) {
console.error("Error formatting script:", err)
setFormattedMarkdown(scriptContent)
} finally {
setIsFormatting(false)
}
}
formatScriptContent()
}
}, [isScriptReady, scriptContent, isFormatting, fileName])

useEffect(() => {
return () => {
isMounted.current = false
}
}, [])

useEffect(() => {
return () => {
if (isRecording) {
stopSelfTakeRecording()
}
}
}, [isOpen])

if (!isOpen) return null

return (
<div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 dark:bg-black/80 backdrop-blur-sm p-2 sm:p-4 md:p-0">
<motion.div
initial={{ opacity: 0, scale: 0.95 }}
animate={{ opacity: 1, scale: 1 }}
exit={{ opacity: 0, scale: 0.95 }}
className="relative w-full max-w-full sm:max-w-3xl md:max-w-4xl lg:max-w-6xl h-[90vh] sm:h-[85vh] bg-white dark:bg-gray-900 rounded-2xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700 transition-colors duration-300"
>
<div className="absolute inset-0 bg-gradient-to-t from-gray-50/30 dark:from-gray-800/10 to-transparent pointer-events-none" />
<div className="absolute top-4 left-4 flex flex-col space-y-1">
{sessionStatus === "loading" && (
<div className="bg-yellow-500/20 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
<Loader className="w-3 h-3 mr-1 animate-spin" />
Loading session...
</div>
)}
{sessionStatus === "unauthenticated" && (
<div className="bg-red-500/20 text-red-300 text-xs px-2 py-1 rounded-md flex items-center">
<Info className="w-3 h-3 mr-1" />
Not authenticated
</div>
)}
{apiConfigStatus === 'connecting' && (
<div className="bg-yellow-500/20 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
<Loader className="w-3 h-3 mr-1 animate-spin" />
Connecting to voice API...
</div>
)}
{apiConfigStatus === 'invalid' && (
<div className="bg-red-500/20 text-red-300 text-xs px-2 py-1 rounded-md flex items-center">
<Info className="w-3 h-3 mr-1" />
ElevenLabs API issue
</div>
)}
</div>
<div className="flex flex-col md:flex-row h-full">
<AnimatePresence>
{(isMobileSidebarOpen || isOpen) && (
<motion.div
key="sidebar-motion"
initial={{ x: -300, opacity: 0 }}
animate={{ x: 0, opacity: 1 }}
exit={{ x: -300, opacity: 0 }}
transition={{ duration: 0.2 }}
>
<SideBar
activeTab={activeTab}
setActiveTab={handleScriptSelection}
scriptFiles={scriptFiles}
loading={loading}
error={error}
setError={setError}
isUploading={isUploading}
uploadProgress={uploadProgress}
uploadStatusText={uploadStatusText}
handleUploadClick={handleUploadClick}
handleFileUpload={handleFileUpload}
fileInputRef={fileInputRef}
sessionStatus={sessionStatus}
session={session}
onClose={onClose}
isMobileSidebarOpen={isMobileSidebarOpen}
setIsMobileSidebarOpen={setIsMobileSidebarOpen}
/>
</motion.div>
)}
</AnimatePresence>
<div className="flex-1 overflow-hidden backdrop-blur-sm">
<div className="h-full flex flex-col">
{isRecording && (
<div className="bg-red-600/90 backdrop-blur-sm px-4 py-2 flex items-center justify-between">
<div className="flex items-center space-x-3">
<div className="flex items-center space-x-2">
<div className="w-3 h-3 bg-red-300 rounded-full animate-pulse"></div>
<span className="text-white font-medium text-sm">Recording Self Tape</span>
</div>
<span className="text-red-100 text-xs">
Recording will continue while you navigate between tabs
</span>
</div>
<button
onClick={stopSelfTakeRecording}
className="bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
>
Stop Recording
</button>
</div>
)}
<div className="border-b border-gray-200 dark:border-gray-700 px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 dark:bg-gray-800/50 transition-colors duration-300">
<div className="flex flex-wrap gap-4 sm:space-x-6 pt-3 justify-between items-center">
<div className="flex flex-wrap gap-4 sm:space-x-6">
{[
{ name: "rehearsing", label: "Connecting", icon: <Mic className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
{ name: "chat", label: "Tutor", icon: <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
{ name: "script", label: "Script", icon: <Book className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
{
name: "response",
label: "Self Tape",
icon: <MessageCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />,
hasRecordingIndicator: isRecording && activeSection !== "response"
},
{ name: "details", label: "Details", icon: <FileText className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
].map((section) => (
<button
key={section.name}
onClick={() => setActiveSection(section.name)}
className={`relative text-xs sm:text-sm font-medium px-3 py-2 rounded-md transition-all duration-200 flex items-center border-b-2 ${ activeSection === section.name ? "border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-400 bg-blue-50/50 dark:bg-blue-500/10" : "border-transparent text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-gray-50 dark:hover:bg-white/5" } ${ ['rehearsing', 'chat', 'response', 'details'].includes(section.name) ? 'courier-font' : '' }`}
>
{section.icon}
{section.label}
{section.hasRecordingIndicator && (
<div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
)}
</button>
))}
</div>
<div className="flex items-center space-x-2">
<CompactThemeToggle />
<button
onClick={(e) => {
e.preventDefault()
e.stopPropagation()
onClose()
}}
className="hidden md:flex p-1.5 rounded-full bg-gray-100 dark:bg-gray-600/80 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-600 dark:text-white transition-all duration-300 shadow-sm items-center justify-center border border-gray-200 dark:border-transparent hover:border-gray-300 dark:hover:border-gray-400/50"
aria-label="Close"
type="button"
title="Close Script Reader"
>
<X className="w-3.5 h-3.5" />
</button>
</div>
</div>
</div>
<div className="flex-1 overflow-y-auto p-4 sm:p-6 scrollbar-thin scrollbar-track-gray-100 dark:scrollbar-track-gray-800 scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 hover:scrollbar-thumb-gray-400 dark:hover:scrollbar-thumb-gray-500">
<AnimatePresence mode="wait">
<motion.div
key={`${activeTab}-${activeSection}`}
initial={{ opacity: 0, y: 20 }}
animate={{ opacity: 1, y: 0 }}
exit={{ opacity: 0, y: -20 }}
transition={{ duration: 0.2 }}
className="space-y-6 h-full"
>
{activeSection === "rehearsing" && (
<Rehearsals
apiConfigStatus={apiConfigStatus}
detailedErrorInfo={detailedErrorInfo}
isListening={isListening}
voiceStatus={voiceStatus}
isMuted={isMuted}
isSpeaking={isSpeaking}
hasPermission={hasPermission}
voiceErrorMessage={voiceErrorMessage}
toggleMute={toggleMute}
handleEndConversation={handleEndConversation}
handleStartConversation={handleStartConversation}
setVoiceErrorMessage={setVoiceErrorMessage}
selectedScriptName={fileName || undefined}
selectedVoiceId={selectedVoiceId}
onVoiceSelect={handleVoiceSelect}
isUpdatingVoice={isUpdatingVoice}
agentModality={agentModality}
onAgentModalityChange={setAgentModality}
onSwitchToScriptTab={() => setActiveSection('script')}
conversationMessages={conversationMessages}
/>
)}
{activeSection === "chat" && <ChatTab chatId={activeTab ?? ''} namespace={namespace} />}
{activeSection === "script" && (
<ScriptTab
scriptContent={scriptContent}
isScriptLoading={isScriptLoading || isFormatting}
isScriptReady={isScriptReady && !isFormatting}
scriptName={fileName}
scriptId={activeTab}
isListening={isListening}
isMuted={isMuted}
toggleMute={toggleMute}
handleEndConversation={handleEndConversation}
handleStartConversation={handleStartConversation}
onScriptDeleted={handleScriptDeleted}
apiConfigStatus={apiConfigStatus}
hasPermission={hasPermission}
voiceStatus={voiceStatus}
selectedVoiceId={selectedVoiceId}
scriptValidationStatus={scriptValidationStatus}
/>
)}
{activeSection === "response" && (
<ResponseTab
isConnected={voiceStatus === 'connected'}
conversationMessages={conversationMessages}
onClearMessages={() => setConversationMessages([])}
onAddTestMessages={addTestMessages}
scriptName={fileName}
voiceId={selectedVoiceId}
sessionDuration={sessionDuration}
voiceStatus={voiceStatus}
isRecording={isRecording}
recordingError={recordingError}
recordings={recordings}
playingRecording={playingRecording}
recordingMode={recordingMode}
recordingDuration={recordingDuration}
currentStream={currentStream}
hasCameraPermission={hasCameraPermission}
onStartRecording={startSelfTakeRecording}
onStopRecording={stopSelfTakeRecording}
onTogglePlayback={toggleRecordingPlayback}
onDeleteRecording={deleteRecording}
onRecordingModeChange={setRecordingMode}
onLoadRecordings={loadAllRecordings}
onRequestCameraPermission={requestCameraPermission}
/>
)}
{activeSection === "details" && (
<FileDetails
activeTab={activeTab}
fileName={fileName}
namespace={namespace}
apiConfigStatus={apiConfigStatus}
sessionStatus={sessionStatus}
detailedErrorInfo={detailedErrorInfo}
/>
)}
</motion.div>
</AnimatePresence>
</div>
</div>
</div>
</div>
</motion.div>
</div>
)
}

export default Readermodal
